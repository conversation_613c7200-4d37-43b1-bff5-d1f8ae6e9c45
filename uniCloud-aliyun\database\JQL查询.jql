// 房屋租赁小程序数据库初始化和查询脚本
// 使用JQL语法操作项目关联的uniCloud空间的数据库

// 1. 创建房源集合并插入测试数据
// db.collection('house').add({
//   title: "精装两居室，拎包入住",
//   desc: "房间干净整洁，家具家电齐全，交通便利，周边配套设施完善",
//   images: [
//     "https://example.com/house1.jpg",
//     "https://example.com/house2.jpg"
//   ],
//   location: {
//     address: "北京市朝阳区三里屯街道",
//     latitude: 39.9042,
//     longitude: 116.4074
//   },
//   price: 3500,
//   type: "二居室",
//   config: ["空调", "洗衣机", "冰箱", "热水器", "WiFi", "床"],
//   contact: {
//     phone: "13800138000",
//     wechat: "landlord123"
//   },
//   owner_id: "user123",
//   status: "approved",
//   view_count: 0,
//   created_at: new Date(),
//   updated_at: new Date()
// })

// 2. 创建轮播图集合
// db.collection('banner').add({
//   title: "优质房源推荐",
//   image: "https://example.com/banner1.jpg",
//   link: "",
//   sort: 1,
//   status: "active",
//   created_at: new Date()
// })

// 3. 查询所有房源
db.collection('house').get();

// 4. 查询审核通过的房源
// db.collection('house').where({status: 'approved'}).get();

// 5. 查询用户信息
// db.collection('uni-id-users').get();

// 6. 查询轮播图
// db.collection('banner').where({status: 'active'}).orderBy('sort', 'asc').get();
