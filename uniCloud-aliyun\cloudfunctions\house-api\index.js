'use strict';

const db = uniCloud.database()
const collection = db.collection('house')

exports.main = async (event, context) => {
	const { action, data } = event
	
	try {
		switch (action) {
			case 'getHouseList':
				return await getHouseList(data)
			case 'getHouseDetail':
				return await getHouseDetail(data)
			case 'postHouse':
				return await postHouse(data)
			case 'updateHouse':
				return await updateHouse(data)
			case 'deleteHouse':
				return await deleteHouse(data)
			case 'searchHouse':
				return await searchHouse(data)
			case 'toggleFavorite':
				return await toggleFavorite(data)
			default:
				return {
					code: 400,
					message: '无效的操作类型'
				}
		}
	} catch (error) {
		console.error('云函数执行错误:', error)
		return {
			code: 500,
			message: '服务器内部错误',
			error: error.message
		}
	}
}

// 获取房源列表
async function getHouseList(data) {
	const { page = 1, pageSize = 10, filters = {} } = data
	const skip = (page - 1) * pageSize
	
	let query = collection.where({
		status: 'approved' // 只显示审核通过的房源
	})
	
	// 添加筛选条件
	if (filters.area) {
		query = query.where({
			'location.address': new RegExp(filters.area)
		})
	}
	
	if (filters.priceRange) {
		const [minPrice, maxPrice] = filters.priceRange.split('-').map(Number)
		if (maxPrice === 999999) {
			query = query.where({
				price: db.command.gte(minPrice)
			})
		} else {
			query = query.where({
				price: db.command.gte(minPrice).and(db.command.lte(maxPrice))
			})
		}
	}
	
	if (filters.type) {
		query = query.where({
			type: filters.type
		})
	}
	
	const result = await query
		.orderBy('created_at', 'desc')
		.skip(skip)
		.limit(pageSize)
		.get()
	
	return {
		code: 200,
		data: result.data,
		hasMore: result.data.length === pageSize
	}
}

// 获取房源详情
async function getHouseDetail(data) {
	const { houseId } = data
	
	const result = await collection.doc(houseId).get()
	
	if (result.data.length === 0) {
		return {
			code: 404,
			message: '房源不存在'
		}
	}
	
	// 增加浏览次数
	await collection.doc(houseId).update({
		view_count: db.command.inc(1)
	})
	
	return {
		code: 200,
		data: result.data[0]
	}
}

// 发布房源
async function postHouse(data) {
	const { houseInfo, userId } = data
	
	// 验证必填字段
	if (!houseInfo.title || !houseInfo.price || !houseInfo.type || !houseInfo.location.address) {
		return {
			code: 400,
			message: '请填写完整的房源信息'
		}
	}
	
	const houseData = {
		...houseInfo,
		owner_id: userId,
		status: 'pending', // 待审核
		view_count: 0,
		created_at: new Date(),
		updated_at: new Date()
	}
	
	const result = await collection.add(houseData)
	
	return {
		code: 200,
		message: '房源发布成功，等待审核',
		data: { houseId: result.id }
	}
}

// 更新房源
async function updateHouse(data) {
	const { houseId, houseInfo, userId } = data
	
	// 验证房源所有者
	const house = await collection.doc(houseId).get()
	if (house.data.length === 0) {
		return {
			code: 404,
			message: '房源不存在'
		}
	}
	
	if (house.data[0].owner_id !== userId) {
		return {
			code: 403,
			message: '无权限修改此房源'
		}
	}
	
	const updateData = {
		...houseInfo,
		updated_at: new Date(),
		status: 'pending' // 修改后重新审核
	}
	
	await collection.doc(houseId).update(updateData)
	
	return {
		code: 200,
		message: '房源更新成功'
	}
}

// 删除房源
async function deleteHouse(data) {
	const { houseId, userId } = data
	
	// 验证房源所有者
	const house = await collection.doc(houseId).get()
	if (house.data.length === 0) {
		return {
			code: 404,
			message: '房源不存在'
		}
	}
	
	if (house.data[0].owner_id !== userId) {
		return {
			code: 403,
			message: '无权限删除此房源'
		}
	}
	
	await collection.doc(houseId).remove()
	
	return {
		code: 200,
		message: '房源删除成功'
	}
}

// 搜索房源
async function searchHouse(data) {
	const { keyword, page = 1, pageSize = 10 } = data
	const skip = (page - 1) * pageSize
	
	if (!keyword) {
		return await getHouseList({ page, pageSize })
	}
	
	const result = await collection
		.where({
			status: 'approved'
		})
		.where(db.command.or([
			{
				title: new RegExp(keyword, 'i')
			},
			{
				desc: new RegExp(keyword, 'i')
			},
			{
				'location.address': new RegExp(keyword, 'i')
			}
		]))
		.orderBy('created_at', 'desc')
		.skip(skip)
		.limit(pageSize)
		.get()
	
	return {
		code: 200,
		data: result.data,
		hasMore: result.data.length === pageSize
	}
}

// 切换收藏状态
async function toggleFavorite(data) {
	const { houseId, userId } = data
	
	const userCollection = db.collection('user')
	const user = await userCollection.doc(userId).get()
	
	if (user.data.length === 0) {
		return {
			code: 404,
			message: '用户不存在'
		}
	}
	
	const favorites = user.data[0].favorites || []
	const index = favorites.indexOf(houseId)
	
	if (index > -1) {
		// 取消收藏
		favorites.splice(index, 1)
	} else {
		// 添加收藏
		favorites.push(houseId)
	}
	
	await userCollection.doc(userId).update({
		favorites: favorites
	})
	
	return {
		code: 200,
		message: index > -1 ? '已取消收藏' : '已添加收藏',
		data: { isFavorite: index === -1 }
	}
}
