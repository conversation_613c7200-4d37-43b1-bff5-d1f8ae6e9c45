<template>
	<view class="post-house-container">
		<!-- 房源图片 -->
		<view class="form-section">
			<u-text text="房源图片 *" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<view class="image-upload">
				<view class="image-item" v-for="(image, index) in houseForm.images" :key="index">
					<u-image
						:src="image"
						mode="aspectFill"
						width="200rpx"
						height="200rpx"
						border-radius="10rpx"
					></u-image>
					<view class="image-delete" @click="deleteImage(index)">
						<u-icon name="close" color="#fff" size="24"></u-icon>
					</view>
				</view>
				<view class="upload-btn" @click="chooseImage" v-if="houseForm.images.length < 9">
					<u-icon name="camera" size="48" color="#999"></u-icon>
					<u-text text="添加图片" size="24" color="#999" style="margin-top: 10rpx;"></u-text>
					<u-text :text="`${houseForm.images.length}/9`" size="20" color="#999" style="margin-top: 5rpx;"></u-text>
				</view>
			</view>
		</view>
			
		<!-- 基本信息 -->
		<view class="form-section">
			<u-text text="基本信息" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<u-form :model="houseForm" ref="houseForm" :rules="formRules">
				<u-form-item label="房源标题" prop="title" border-bottom required>
					<u-input
						v-model="houseForm.title"
						placeholder="请输入房源标题"
						maxlength="50"
						clearable
					></u-input>
				</u-form-item>

				<u-form-item label="租金" prop="price" border-bottom required>
					<u-input
						v-model="houseForm.price"
						placeholder="请输入租金"
						type="number"
						suffix-icon="rmb-circle-fill"
						clearable
					>
						<template #suffix>
							<u-text text="元/月" size="28" color="#666"></u-text>
						</template>
					</u-input>
				</u-form-item>

				<u-form-item label="户型" prop="type" border-bottom required @click="showTypePicker">
					<u-input
						v-model="houseForm.type"
						placeholder="请选择户型"
						disabled
						suffix-icon="arrow-right"
					></u-input>
				</u-form-item>
			</u-form>
		</view>
			
		<!-- 位置信息 -->
		<view class="form-section">
			<u-text text="位置信息" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<u-form-item label="详细地址" border-bottom required @click="chooseLocation">
				<u-input
					v-model="houseForm.location.address"
					placeholder="点击选择位置"
					disabled
					suffix-icon="map"
				></u-input>
			</u-form-item>
		</view>

		<!-- 房源配置 -->
		<view class="form-section">
			<u-text text="房源配置" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<view class="config-grid">
				<u-checkbox-group v-model="houseForm.config" placement="row">
					<u-checkbox
						v-for="(config, index) in configOptions"
						:key="index"
						:name="config"
						:label="config"
						size="32"
						style="margin-right: 30rpx; margin-bottom: 20rpx;"
					></u-checkbox>
				</u-checkbox-group>
			</view>
		</view>
			
		<!-- 房源描述 -->
		<view class="form-section">
			<u-text text="房源描述" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<u-textarea
				v-model="houseForm.desc"
				placeholder="请详细描述房源情况，如周边环境、交通便利性等"
				maxlength="500"
				count
				height="200"
			></u-textarea>
		</view>

		<!-- 联系方式 -->
		<view class="form-section">
			<u-text text="联系方式" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<u-form-item label="联系电话" border-bottom>
				<u-input
					v-model="houseForm.contact.phone"
					placeholder="请输入联系电话"
					type="number"
					prefix-icon="phone"
					clearable
				></u-input>
			</u-form-item>

			<u-form-item label="微信号" border-bottom>
				<u-input
					v-model="houseForm.contact.wechat"
					placeholder="请输入微信号"
					prefix-icon="chat"
					clearable
				></u-input>
			</u-form-item>
		</view>
			
		<!-- 提交按钮 -->
		<view class="submit-section">
			<u-button
				:text="submitting ? '发布中...' : '发布房源'"
				type="primary"
				size="large"
				:loading="submitting"
				@click="submitHouse"
				style="width: 100%;"
			></u-button>
			<view class="submit-tip">
				<u-text text="发布后需要管理员审核，审核通过后才会显示" size="24" color="#999"></u-text>
			</view>
		</view>

		<!-- 户型选择器 -->
		<u-picker
			:show="showTypeSelector"
			:columns="typeColumns"
			@confirm="onTypeConfirm"
			@cancel="showTypeSelector = false"
		></u-picker>
	</view>
</template>

<script>
import api from '@/utils/api.js'

export default {
	data() {
		return {
			houseForm: {
				title: '',
				price: '',
				type: '',
				location: {
					address: '',
					latitude: '',
					longitude: ''
				},
				config: [],
				desc: '',
				contact: {
					phone: '',
					wechat: ''
				},
				images: []
			},
			formRules: {
				title: [
					{
						required: true,
						message: '请输入房源标题',
						trigger: 'blur'
					}
				],
				price: [
					{
						required: true,
						message: '请输入租金',
						trigger: 'blur'
					}
				],
				type: [
					{
						required: true,
						message: '请选择户型',
						trigger: 'change'
					}
				]
			},
			typeColumns: [
				['一居室', '二居室', '三居室', '四居室', '合租']
			],
			showTypeSelector: false,
			configOptions: [
				'空调', '洗衣机', '冰箱', '热水器', '电视', 'WiFi',
				'床', '衣柜', '桌椅', '独立卫生间', '阳台', '电梯'
			],
			submitting: false
		}
	},
	methods: {
		chooseImage() {
			const remainCount = 9 - this.houseForm.images.length
			uni.chooseImage({
				count: remainCount,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadImages(res.tempFilePaths)
				},
				fail: (error) => {
					console.error('选择图片失败:', error)
				}
			})
		},

		async uploadImages(filePaths) {
			try {
				uni.showLoading({
					title: '上传中...'
				})

				// 使用 API 上传图片
				const result = await api.uploadImages(filePaths, 'house')
				this.houseForm.images.push(...result.data)

				uni.hideLoading()
				uni.showToast({
					title: '上传成功',
					icon: 'success'
				})
			} catch (error) {
				uni.hideLoading()
				console.error('上传图片失败:', error)
				uni.showToast({
					title: '上传失败',
					icon: 'none'
				})
			}
		},
		deleteImage(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这张图片吗？',
				success: (res) => {
					if (res.confirm) {
						this.houseForm.images.splice(index, 1)
					}
				}
			})
		},

		showTypePicker() {
			this.showTypeSelector = true
		},

		onTypeConfirm(value) {
			this.houseForm.type = value.value[0]
			this.showTypeSelector = false
		},
		chooseLocation() {
			uni.chooseLocation({
				success: (res) => {
					this.houseForm.location = {
						address: res.address,
						latitude: res.latitude,
						longitude: res.longitude
					}
				},
				fail: (err) => {
					console.error('选择位置失败:', err)
					uni.showToast({
						title: '获取位置失败，请检查定位权限',
						icon: 'none'
					})
				}
			})
		},
		async validateForm() {
			// 验证图片
			if (this.houseForm.images.length === 0) {
				uni.showToast({
					title: '请上传房源图片',
					icon: 'none'
				})
				return false
			}

			// 验证表单
			try {
				await this.$refs.houseForm.validate()
			} catch (error) {
				return false
			}

			// 验证位置
			if (!this.houseForm.location.address) {
				uni.showToast({
					title: '请选择位置',
					icon: 'none'
				})
				return false
			}

			// 验证联系方式
			if (!this.houseForm.contact.phone && !this.houseForm.contact.wechat) {
				uni.showToast({
					title: '请至少填写一种联系方式',
					icon: 'none'
				})
				return false
			}

			return true
		},
		async submitHouse() {
			const isValid = await this.validateForm()
			if (!isValid) {
				return
			}

			try {
				this.submitting = true

				// 调用 API 提交房源信息
				await api.postHouse(this.houseForm)

				uni.showModal({
					title: '发布成功',
					content: '房源已提交，等待管理员审核',
					showCancel: false,
					success: () => {
						// 重置表单
						this.resetForm()
						// 跳转到我的发布页面
						uni.switchTab({
							url: '/pages/my/my'
						})
					}
				})
			} catch (error) {
				console.error('发布房源失败:', error)
				uni.showToast({
					title: error.message || '发布失败',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		},
		resetForm() {
			this.houseForm = {
				title: '',
				price: '',
				type: '',
				location: {
					address: '',
					latitude: '',
					longitude: ''
				},
				config: [],
				desc: '',
				contact: {
					phone: '',
					wechat: ''
				},
				images: []
			}
			this.showTypeSelector = false
			// 重置表单验证状态
			this.$nextTick(() => {
				this.$refs.houseForm?.clearValidate()
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.post-house-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

.form-section {
	background-color: #fff;
	margin: 20rpx;
	padding: 30rpx;
	border-radius: 20rpx;
}

.image-upload {
	display: flex;
	flex-wrap: wrap;
}

.image-item {
	position: relative;
	width: 200rpx;
	height: 200rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
}

.image-delete {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background-color: rgba(255, 71, 87, 0.8);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
}

.upload-btn {
	width: 200rpx;
	height: 200rpx;
	border: 2rpx dashed #ddd;
	border-radius: 10rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #999;
	transition: all 0.3s ease;

	&:active {
		background-color: #f8f9fa;
		border-color: #007AFF;
	}
}

.config-grid {
	display: flex;
	flex-wrap: wrap;
}

.submit-section {
	padding: 30rpx;
}

.submit-tip {
	text-align: center;
	margin-top: 20rpx;
}
</style>
