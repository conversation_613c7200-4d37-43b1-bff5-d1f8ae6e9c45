<template>
	<view class="my-container">
		<!-- 用户信息 -->
		<view class="user-info">
			<view class="user-avatar" @click="login" v-if="!userInfo.nickname">
				<image src="/static/default-avatar.png" class="avatar-image"></image>
				<text class="login-text">点击登录</text>
			</view>
			<view class="user-profile" v-else>
				<image :src="userInfo.avatar" class="avatar-image"></image>
				<view class="user-details">
					<text class="user-name">{{ userInfo.nickname }}</text>
					<text class="user-phone">{{ userInfo.phone || '未绑定手机号' }}</text>
				</view>
			</view>
		</view>
		
		<!-- 数据统计 -->
		<view class="stats-section" v-if="userInfo.nickname">
			<view class="stats-item">
				<text class="stats-number">{{ userStats.favorites }}</text>
				<text class="stats-label">收藏</text>
			</view>
			<view class="stats-item">
				<text class="stats-number">{{ userStats.posts }}</text>
				<text class="stats-label">发布</text>
			</view>
			<view class="stats-item">
				<text class="stats-number">{{ userStats.views }}</text>
				<text class="stats-label">浏览</text>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="goToFavorites">
				<view class="menu-left">
					<text class="menu-icon">❤️</text>
					<text class="menu-text">我的收藏</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="goToMyPosts">
				<view class="menu-left">
					<text class="menu-icon">🏠</text>
					<text class="menu-text">我的发布</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="goToBrowseHistory">
				<view class="menu-left">
					<text class="menu-icon">👁️</text>
					<text class="menu-text">浏览记录</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
		</view>
		
		<!-- 设置菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="bindPhone" v-if="userInfo.nickname && !userInfo.phone">
				<view class="menu-left">
					<text class="menu-icon">📱</text>
					<text class="menu-text">绑定手机号</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="showAbout">
				<view class="menu-left">
					<text class="menu-icon">ℹ️</text>
					<text class="menu-text">关于我们</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="contactService">
				<view class="menu-left">
					<text class="menu-icon">💬</text>
					<text class="menu-text">联系客服</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="logout" v-if="userInfo.nickname">
				<view class="menu-left">
					<text class="menu-icon">🚪</text>
					<text class="menu-text">退出登录</text>
				</view>
				<text class="menu-arrow">></text>
			</view>
		</view>
		
		<!-- 版本信息 -->
		<view class="version-info">
			<text>版本号：v1.0.0</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				nickname: '',
				avatar: '',
				phone: ''
			},
			userStats: {
				favorites: 0,
				posts: 0,
				views: 0
			}
		}
	},
	onShow() {
		this.loadUserInfo()
		if (this.userInfo.nickname) {
			this.loadUserStats()
		}
	},
	methods: {
		loadUserInfo() {
			// TODO: 从本地存储或云函数获取用户信息
			const userInfo = uni.getStorageSync('userInfo')
			if (userInfo) {
				this.userInfo = userInfo
			}
		},
		loadUserStats() {
			// TODO: 调用云函数获取用户统计数据
			console.log('加载用户统计数据')
		},
		login() {
			// 微信登录
			uni.getUserProfile({
				desc: '用于完善用户资料',
				success: (res) => {
					console.log('获取用户信息成功:', res)
					// TODO: 调用云函数进行微信登录
					this.userInfo = {
						nickname: res.userInfo.nickName,
						avatar: res.userInfo.avatarUrl,
						phone: ''
					}
					uni.setStorageSync('userInfo', this.userInfo)
					this.loadUserStats()
				},
				fail: (err) => {
					console.error('获取用户信息失败:', err)
					uni.showToast({
						title: '登录失败',
						icon: 'none'
					})
				}
			})
		},
		goToFavorites() {
			if (!this.checkLogin()) return
			uni.navigateTo({
				url: '/pages/my/favorites'
			})
		},
		goToMyPosts() {
			if (!this.checkLogin()) return
			uni.navigateTo({
				url: '/pages/my/my-posts'
			})
		},
		goToBrowseHistory() {
			if (!this.checkLogin()) return
			uni.navigateTo({
				url: '/pages/my/browse-history'
			})
		},
		bindPhone() {
			uni.showModal({
				title: '绑定手机号',
				content: '是否绑定手机号以便更好地为您服务？',
				success: (res) => {
					if (res.confirm) {
						// TODO: 实现手机号绑定功能
						uni.showToast({
							title: '功能开发中',
							icon: 'none'
						})
					}
				}
			})
		},
		showAbout() {
			uni.showModal({
				title: '关于我们',
				content: '房屋租赁小程序 v1.0.0\n为您提供便捷的租房服务',
				showCancel: false
			})
		},
		contactService() {
			uni.showActionSheet({
				itemList: ['拨打客服电话', '复制微信号'],
				success: (res) => {
					if (res.tapIndex === 0) {
						uni.makePhoneCall({
							phoneNumber: '************'
						})
					} else if (res.tapIndex === 1) {
						uni.setClipboardData({
							data: 'service_wechat',
							success: () => {
								uni.showToast({
									title: '微信号已复制',
									icon: 'success'
								})
							}
						})
					}
				}
			})
		},
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除用户信息
						this.userInfo = {
							nickname: '',
							avatar: '',
							phone: ''
						}
						this.userStats = {
							favorites: 0,
							posts: 0,
							views: 0
						}
						uni.removeStorageSync('userInfo')
						uni.removeStorageSync('token')
						uni.showToast({
							title: '已退出登录',
							icon: 'success'
						})
					}
				}
			})
		},
		checkLogin() {
			if (!this.userInfo.nickname) {
				uni.showModal({
					title: '请先登录',
					content: '需要登录后才能使用此功能',
					success: (res) => {
						if (res.confirm) {
							this.login()
						}
					}
				})
				return false
			}
			return true
		}
	}
}
</script>

<style scoped>
.my-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.user-info {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	padding: 60rpx 30rpx 40rpx;
	color: #fff;
}

.user-avatar {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.user-profile {
	display: flex;
	align-items: center;
}

.avatar-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	margin-bottom: 20rpx;
}

.user-profile .avatar-image {
	margin-bottom: 0;
	margin-right: 30rpx;
}

.login-text {
	font-size: 28rpx;
	opacity: 0.9;
}

.user-details {
	flex: 1;
}

.user-name {
	font-size: 36rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.user-phone {
	font-size: 26rpx;
	opacity: 0.8;
}

.stats-section {
	display: flex;
	background-color: #fff;
	margin: -20rpx 30rpx 20rpx;
	border-radius: 20rpx;
	padding: 40rpx 0;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stats-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #007AFF;
	margin-bottom: 10rpx;
}

.stats-label {
	font-size: 26rpx;
	color: #666;
}

.menu-section {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.menu-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #f8f8f8;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-left {
	display: flex;
	align-items: center;
}

.menu-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
}

.menu-text {
	font-size: 30rpx;
	color: #333;
}

.menu-arrow {
	font-size: 28rpx;
	color: #999;
}

.version-info {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 24rpx;
}
</style>
