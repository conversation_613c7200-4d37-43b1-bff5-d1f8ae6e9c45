# 房屋租赁小程序开发计划

## 项目概述
基于 UniApp + Vue2 + UniCloud 开发完整的房屋租赁微信小程序系统

## 技术架构
- **前端**: UniApp + Vue2 + uView UI 2.0
- **后端**: UniCloud 云函数 + 云数据库 + 云存储
- **管理后台**: UniApp Admin (H5版本)
- **地图服务**: 腾讯地图 API
- **用户认证**: uni-id + 微信登录

## 功能模块

### 用户端 (微信小程序)
1. **首页** - Banner轮播图、搜索栏、推荐房源、快捷入口
2. **房源列表页** - 分类筛选、房源展示
3. **房源详情页** - 图集轮播、详细信息、联系房东、收藏
4. **发布房源页** - 房源信息填写、图片上传、地理定位
5. **个人中心** - 我的收藏、我的发布、账号设置

### 管理后台 (Web系统)
1. **登录页** - 管理员认证
2. **数据统计** - 房源数量、用户统计、访问量
3. **房源管理** - 审核、编辑、删除房源
4. **用户管理** - 用户列表、封禁管理
5. **系统设置** - 公告管理、参数配置

## 数据库设计

### house 集合 (房源表)
- _id: 房源ID
- title: 房源标题
- desc: 房源描述
- images: 图片URL数组
- location: 位置信息(地址+经纬度)
- price: 租金
- type: 房型
- config: 配置数组
- contact: 联系方式
- owner_id: 发布用户ID
- status: 审核状态
- created_at: 创建时间

### user 集合 (用户表)
- _id: 用户ID
- nickname: 微信昵称
- avatar: 头像URL
- phone: 手机号
- openid: 微信openid
- is_banned: 是否被封禁
- favorites: 收藏房源ID数组
- created_at: 注册时间

## 开发阶段

### 第一阶段: 项目基础架构搭建
1. 配置开发环境
2. 数据库设计与初始化
3. 云函数基础架构

### 第二阶段: 小程序前端开发
1. 公共组件开发
2. 核心页面开发
3. 用户认证与权限

### 第三阶段: 管理后台开发
1. 管理后台基础框架
2. 管理功能开发

### 第四阶段: 高级功能与优化
1. 地图与定位功能
2. 图片处理与云存储
3. 性能优化与测试

## 当前状态
正在执行第一阶段：项目基础架构搭建
