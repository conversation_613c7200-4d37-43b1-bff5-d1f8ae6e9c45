<template>
	<view class="home-container">
		<!-- Banner轮播图 -->
		<u-swiper
			:list="bannerList"
			:autoplay="true"
			:interval="3000"
			:duration="500"
			indicator
			height="400"
			border-radius="0"
			@click="onBannerClick"
		></u-swiper>

		<!-- 搜索栏 -->
		<search-bar
			:disabled="true"
			placeholder="搜索房源、地址、关键词"
			@click="goToSearch"
		></search-bar>
		
		<!-- 快捷入口 -->
		<view class="quick-entry">
			<u-grid :col="4" :border="false">
				<u-grid-item @click="goToMap">
					<view class="grid-item">
						<u-icon name="map" size="48" color="#007AFF"></u-icon>
						<u-text text="地图找房" size="24" color="#666" style="margin-top: 10rpx;"></u-text>
					</view>
				</u-grid-item>
				<u-grid-item @click="goToPost">
					<view class="grid-item">
						<u-icon name="plus-circle" size="48" color="#19be6b"></u-icon>
						<u-text text="发布房源" size="24" color="#666" style="margin-top: 10rpx;"></u-text>
					</view>
				</u-grid-item>
				<u-grid-item @click="goToFavorites">
					<view class="grid-item">
						<u-icon name="heart" size="48" color="#ff6b35"></u-icon>
						<u-text text="我的收藏" size="24" color="#666" style="margin-top: 10rpx;"></u-text>
					</view>
				</u-grid-item>
				<u-grid-item @click="goToMyPosts">
					<view class="grid-item">
						<u-icon name="home" size="48" color="#ffb700"></u-icon>
						<u-text text="我的发布" size="24" color="#666" style="margin-top: 10rpx;"></u-text>
					</view>
				</u-grid-item>
			</u-grid>
		</view>
		
		<!-- 推荐房源 -->
		<view class="recommend-section">
			<view class="section-header">
				<u-text text="推荐房源" size="32" bold color="#333"></u-text>
				<u-text text="更多 >" size="28" color="#007AFF" @click="goToHouseList"></u-text>
			</view>

			<view class="house-list">
				<house-card
					v-for="(house, index) in recommendHouses"
					:key="index"
					:house-info="house"
					@favorite-change="onFavoriteChange"
				></house-card>

				<!-- 空状态 -->
				<u-empty
					v-if="recommendHouses.length === 0 && !loading"
					mode="list"
					text="暂无推荐房源"
				></u-empty>

				<!-- 加载状态 -->
				<u-loading-page
					:loading="loading"
					loading-text="加载中..."
				></u-loading-page>
			</view>
		</view>
	</view>
</template>

<script>
import api from '@/utils/api.js'

export default {
	components: {
		'search-bar': () => import('@/components/search-bar/search-bar.vue'),
		'house-card': () => import('@/components/house-card/house-card.vue')
	},
	data() {
		return {
			bannerList: [
				{ image: '/static/banner1.jpg' },
				{ image: '/static/banner2.jpg' },
				{ image: '/static/banner3.jpg' }
			],
			recommendHouses: [],
			loading: false
		}
	},
	onLoad() {
		this.loadRecommendHouses()
	},
	onShow() {
		// 页面显示时刷新数据
		this.loadRecommendHouses()
	},
	methods: {
		async loadRecommendHouses() {
			try {
				this.loading = true
				const result = await api.getHouseList({
					page: 1,
					pageSize: 10
				})
				this.recommendHouses = result.data || []
			} catch (error) {
				console.error('加载推荐房源失败:', error)
				this.recommendHouses = []
			} finally {
				this.loading = false
			}
		},

		onBannerClick(index) {
			console.log('点击轮播图:', index)
			// TODO: 处理轮播图点击事件
		},

		onFavoriteChange(data) {
			console.log('收藏状态变化:', data)
			// TODO: 处理收藏状态变化
		},
		goToSearch() {
			uni.navigateTo({
				url: '/pages/house-list/house-list'
			})
		},
		goToMap() {
			uni.showToast({
				title: '地图找房功能开发中',
				icon: 'none'
			})
		},
		goToPost() {
			uni.switchTab({
				url: '/pages/post-house/post-house'
			})
		},
		goToFavorites() {
			uni.navigateTo({
				url: '/pages/my/favorites'
			})
		},
		goToMyPosts() {
			uni.navigateTo({
				url: '/pages/my/my-posts'
			})
		},
		goToHouseList() {
			uni.navigateTo({
				url: '/pages/house-list/house-list'
			})
		},
		goToDetail(houseId) {
			uni.navigateTo({
				url: `/pages/house-detail/house-detail?id=${houseId}`
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.home-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.quick-entry {
	background-color: #fff;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx 0;
}

.grid-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
}

.recommend-section {
	margin: 20rpx;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.house-list {
	display: flex;
	flex-direction: column;
}
</style>
