'use strict';

const uniID = require('uni-id-common')
const db = uniCloud.database()

exports.main = async (event, context) => {
	const { action, data } = event
	
	try {
		switch (action) {
			case 'wxLogin':
				return await wxLogin(data)
			case 'getUserInfo':
				return await getUserInfo(data)
			case 'updateUserInfo':
				return await updateUserInfo(data)
			case 'getUserStats':
				return await getUserStats(data)
			case 'getFavorites':
				return await getFavorites(data)
			case 'getMyPosts':
				return await getMyPosts(data)
			default:
				return {
					code: 400,
					message: '无效的操作类型'
				}
		}
	} catch (error) {
		console.error('云函数执行错误:', error)
		return {
			code: 500,
			message: '服务器内部错误',
			error: error.message
		}
	}
}

// 微信登录
async function wxLogin(data) {
	const { code, userInfo } = data
	
	try {
		// 使用 uni-id 进行微信登录
		const uniIdIns = uniID.createInstance({
			context: context
		})
		
		const loginResult = await uniIdIns.loginByWeixin({
			code: code,
			platform: 'mp-weixin'
		})
		
		if (loginResult.errCode !== 0) {
			return {
				code: 400,
				message: loginResult.errMsg || '登录失败'
			}
		}
		
		// 更新用户信息
		const userCollection = db.collection('uni-id-users')
		await userCollection.doc(loginResult.uid).update({
			nickname: userInfo.nickName,
			avatar: userInfo.avatarUrl,
			updated_at: new Date()
		})
		
		return {
			code: 200,
			message: '登录成功',
			data: {
				token: loginResult.token,
				userInfo: {
					uid: loginResult.uid,
					nickname: userInfo.nickName,
					avatar: userInfo.avatarUrl
				}
			}
		}
	} catch (error) {
		console.error('微信登录错误:', error)
		return {
			code: 500,
			message: '登录失败',
			error: error.message
		}
	}
}

// 获取用户信息
async function getUserInfo(data) {
	const { userId } = data
	
	const userCollection = db.collection('uni-id-users')
	const result = await userCollection.doc(userId).get()
	
	if (result.data.length === 0) {
		return {
			code: 404,
			message: '用户不存在'
		}
	}
	
	const user = result.data[0]
	return {
		code: 200,
		data: {
			uid: user._id,
			nickname: user.nickname,
			avatar: user.avatar,
			phone: user.mobile,
			favorites: user.favorites || [],
			created_at: user.register_date
		}
	}
}

// 更新用户信息
async function updateUserInfo(data) {
	const { userId, userInfo } = data
	
	const userCollection = db.collection('uni-id-users')
	const updateData = {
		...userInfo,
		updated_at: new Date()
	}
	
	await userCollection.doc(userId).update(updateData)
	
	return {
		code: 200,
		message: '用户信息更新成功'
	}
}

// 获取用户统计数据
async function getUserStats(data) {
	const { userId } = data
	
	// 获取收藏数量
	const userCollection = db.collection('uni-id-users')
	const user = await userCollection.doc(userId).get()
	const favoritesCount = user.data[0]?.favorites?.length || 0
	
	// 获取发布数量
	const houseCollection = db.collection('house')
	const postsResult = await houseCollection.where({
		owner_id: userId
	}).count()
	
	// 获取浏览数量（这里简化处理，实际可以建立浏览记录表）
	const viewsResult = await houseCollection.where({
		owner_id: userId
	}).field({
		view_count: true
	}).get()
	
	const totalViews = viewsResult.data.reduce((sum, house) => {
		return sum + (house.view_count || 0)
	}, 0)
	
	return {
		code: 200,
		data: {
			favorites: favoritesCount,
			posts: postsResult.total,
			views: totalViews
		}
	}
}

// 获取用户收藏列表
async function getFavorites(data) {
	const { userId, page = 1, pageSize = 10 } = data
	const skip = (page - 1) * pageSize
	
	// 获取用户收藏的房源ID列表
	const userCollection = db.collection('uni-id-users')
	const user = await userCollection.doc(userId).get()
	
	if (user.data.length === 0) {
		return {
			code: 404,
			message: '用户不存在'
		}
	}
	
	const favorites = user.data[0].favorites || []
	
	if (favorites.length === 0) {
		return {
			code: 200,
			data: [],
			hasMore: false
		}
	}
	
	// 分页获取收藏的房源
	const favoriteIds = favorites.slice(skip, skip + pageSize)
	
	const houseCollection = db.collection('house')
	const result = await houseCollection.where({
		_id: db.command.in(favoriteIds)
	}).get()
	
	return {
		code: 200,
		data: result.data,
		hasMore: skip + pageSize < favorites.length
	}
}

// 获取用户发布的房源
async function getMyPosts(data) {
	const { userId, page = 1, pageSize = 10 } = data
	const skip = (page - 1) * pageSize
	
	const houseCollection = db.collection('house')
	const result = await houseCollection
		.where({
			owner_id: userId
		})
		.orderBy('created_at', 'desc')
		.skip(skip)
		.limit(pageSize)
		.get()
	
	return {
		code: 200,
		data: result.data,
		hasMore: result.data.length === pageSize
	}
}
