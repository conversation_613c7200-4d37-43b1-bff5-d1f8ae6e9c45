<template>
	<view class="house-list-container">
		<!-- 搜索栏 -->
		<search-bar
			v-model="keyword"
			:show-action="true"
			action-text="搜索"
			@search="onSearch"
			@action="onSearch"
		></search-bar>

		<!-- 筛选栏 -->
		<view class="filter-bar">
			<u-dropdown>
				<u-dropdown-item
					v-model="selectedAreaIndex"
					title="区域"
					:options="areaOptions"
					@change="onAreaChange"
				></u-dropdown-item>
				<u-dropdown-item
					v-model="selectedPriceIndex"
					title="价格"
					:options="priceOptions"
					@change="onPriceChange"
				></u-dropdown-item>
				<u-dropdown-item
					v-model="selectedTypeIndex"
					title="户型"
					:options="typeOptions"
					@change="onTypeChange"
				></u-dropdown-item>
				<u-dropdown-item
					title="筛选"
					@click="showMoreFilter"
				></u-dropdown-item>
			</u-dropdown>
		</view>
		
		<!-- 房源列表 -->
		<scroll-view scroll-y="true" class="house-scroll" @scrolltolower="loadMore">
			<view class="house-list-content">
				<house-card
					v-for="(house, index) in houseList"
					:key="index"
					:house-info="house"
					@favorite-change="onFavoriteChange"
				></house-card>

				<!-- 空状态 -->
				<u-empty
					v-if="houseList.length === 0 && !loading"
					mode="list"
					:text="keyword ? '未找到相关房源' : '暂无房源'"
				></u-empty>
			</view>

			<!-- 加载更多 -->
			<u-loadmore
				:status="loadmoreStatus"
				:load-text="loadText"
				@loadmore="loadMore"
			></u-loadmore>
		</scroll-view>
	</view>
</template>

<script>
import api from '@/utils/api.js'

export default {
	components: {
		'search-bar': () => import('@/components/search-bar/search-bar.vue'),
		'house-card': () => import('@/components/house-card/house-card.vue')
	},
	data() {
		return {
			houseList: [],
			keyword: '',
			loading: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			loadmoreStatus: 'loadmore',
			loadText: {
				loadmore: '点击或上拉加载更多',
				loading: '正在加载...',
				nomore: '没有更多了'
			},
			// 筛选选项
			selectedAreaIndex: 0,
			selectedPriceIndex: 0,
			selectedTypeIndex: 0,
			areaOptions: [
				{ label: '不限', value: '' },
				{ label: '朝阳区', value: '朝阳区' },
				{ label: '海淀区', value: '海淀区' },
				{ label: '西城区', value: '西城区' },
				{ label: '东城区', value: '东城区' }
			],
			priceOptions: [
				{ label: '不限', value: '' },
				{ label: '1000以下', value: '0-1000' },
				{ label: '1000-2000', value: '1000-2000' },
				{ label: '2000-3000', value: '2000-3000' },
				{ label: '3000以上', value: '3000-999999' }
			],
			typeOptions: [
				{ label: '不限', value: '' },
				{ label: '一居室', value: '一居室' },
				{ label: '二居室', value: '二居室' },
				{ label: '三居室', value: '三居室' },
				{ label: '合租', value: '合租' }
			]
		}
	},
	onLoad(options) {
		// 处理搜索参数
		if (options.keyword) {
			this.keyword = options.keyword
		}
		this.loadHouseList()
	},
	methods: {
		async loadHouseList(reset = false) {
			if (this.loading) return

			try {
				this.loading = true
				this.loadmoreStatus = 'loading'

				if (reset) {
					this.page = 1
					this.houseList = []
					this.hasMore = true
				}

				// 构建筛选条件
				const filters = {}
				if (this.areaOptions[this.selectedAreaIndex]?.value) {
					filters.area = this.areaOptions[this.selectedAreaIndex].value
				}
				if (this.priceOptions[this.selectedPriceIndex]?.value) {
					filters.priceRange = this.priceOptions[this.selectedPriceIndex].value
				}
				if (this.typeOptions[this.selectedTypeIndex]?.value) {
					filters.type = this.typeOptions[this.selectedTypeIndex].value
				}

				let result
				if (this.keyword) {
					result = await api.searchHouse(this.keyword, {
						page: this.page,
						pageSize: this.pageSize
					})
				} else {
					result = await api.getHouseList({
						page: this.page,
						pageSize: this.pageSize,
						filters
					})
				}

				const newHouses = result.data || []
				if (reset) {
					this.houseList = newHouses
				} else {
					this.houseList.push(...newHouses)
				}

				this.hasMore = result.hasMore
				this.loadmoreStatus = this.hasMore ? 'loadmore' : 'nomore'

			} catch (error) {
				console.error('加载房源列表失败:', error)
				this.loadmoreStatus = 'loadmore'
			} finally {
				this.loading = false
			}
		},

		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++
				this.loadHouseList()
			}
		},

		onSearch(keyword) {
			this.keyword = keyword
			this.loadHouseList(true)
		},
		onAreaChange(index) {
			this.selectedAreaIndex = index
			this.loadHouseList(true)
		},

		onPriceChange(index) {
			this.selectedPriceIndex = index
			this.loadHouseList(true)
		},

		onTypeChange(index) {
			this.selectedTypeIndex = index
			this.loadHouseList(true)
		},
		showMoreFilter() {
			uni.showToast({
				title: '更多筛选功能开发中',
				icon: 'none'
			})
		},

		onFavoriteChange(data) {
			console.log('收藏状态变化:', data)
			// TODO: 处理收藏状态变化
		}
	}
}
</script>

<style lang="scss" scoped>
.house-list-container {
	background-color: #f5f5f5;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.filter-bar {
	background-color: #fff;
	border-bottom: 1rpx solid #f0f0f0;
}

.house-scroll {
	flex: 1;
}

.house-list-content {
	padding: 20rpx;
}
</style>
