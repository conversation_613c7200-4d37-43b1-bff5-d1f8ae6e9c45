<template>
	<view class="my-container">
		<!-- 用户信息 -->
		<view class="user-info">
			<view class="user-avatar" @click="login" v-if="!userInfo.nickname">
				<u-avatar
					src="/static/default-avatar.png"
					size="120"
					style="margin-bottom: 20rpx;"
				></u-avatar>
				<u-text text="点击登录" size="28" color="#fff"></u-text>
			</view>
			<view class="user-profile" v-else>
				<u-avatar
					:src="userInfo.avatar"
					size="120"
					style="margin-right: 30rpx;"
				></u-avatar>
				<view class="user-details">
					<u-text
						:text="userInfo.nickname"
						size="36"
						bold
						color="#fff"
						style="margin-bottom: 10rpx;"
					></u-text>
					<u-text
						:text="userInfo.phone || '未绑定手机号'"
						size="26"
						color="rgba(255,255,255,0.8)"
					></u-text>
				</view>
			</view>
		</view>
		
		<!-- 数据统计 -->
		<view class="stats-section" v-if="userInfo.nickname">
			<u-grid :col="3" :border="false">
				<u-grid-item>
					<view class="stats-item">
						<u-text
							:text="userStats.favorites.toString()"
							size="48"
							bold
							color="#007AFF"
						></u-text>
						<u-text
							text="收藏"
							size="26"
							color="#666"
							style="margin-top: 10rpx;"
						></u-text>
					</view>
				</u-grid-item>
				<u-grid-item>
					<view class="stats-item">
						<u-text
							:text="userStats.posts.toString()"
							size="48"
							bold
							color="#19be6b"
						></u-text>
						<u-text
							text="发布"
							size="26"
							color="#666"
							style="margin-top: 10rpx;"
						></u-text>
					</view>
				</u-grid-item>
				<u-grid-item>
					<view class="stats-item">
						<u-text
							:text="userStats.views.toString()"
							size="48"
							bold
							color="#ff6b35"
						></u-text>
						<u-text
							text="浏览"
							size="26"
							color="#666"
							style="margin-top: 10rpx;"
						></u-text>
					</view>
				</u-grid-item>
			</u-grid>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<u-cell-group>
				<u-cell
					title="我的收藏"
					icon="heart"
					is-link
					@click="goToFavorites"
				></u-cell>
				<u-cell
					title="我的发布"
					icon="home"
					is-link
					@click="goToMyPosts"
				></u-cell>
				<u-cell
					title="浏览记录"
					icon="eye"
					is-link
					@click="goToBrowseHistory"
				></u-cell>
			</u-cell-group>
		</view>
		
		<!-- 设置菜单 -->
		<view class="menu-section">
			<u-cell-group>
				<u-cell
					title="绑定手机号"
					icon="phone"
					is-link
					@click="bindPhone"
					v-if="userInfo.nickname && !userInfo.phone"
				></u-cell>
				<u-cell
					title="关于我们"
					icon="info-circle"
					is-link
					@click="showAbout"
				></u-cell>
				<u-cell
					title="联系客服"
					icon="chat"
					is-link
					@click="contactService"
				></u-cell>
				<u-cell
					title="退出登录"
					icon="logout"
					is-link
					@click="logout"
					v-if="userInfo.nickname"
				></u-cell>
			</u-cell-group>
		</view>
		
		<!-- 版本信息 -->
		<view class="version-info">
			<u-text text="版本号：v1.0.0" size="24" color="#999"></u-text>
		</view>
	</view>
</template>

<script>
import api from '@/utils/api.js'

export default {
	data() {
		return {
			userInfo: {
				nickname: '',
				avatar: '',
				phone: ''
			},
			userStats: {
				favorites: 0,
				posts: 0,
				views: 0
			},
			loading: false
		}
	},
	onShow() {
		this.loadUserInfo()
		if (this.userInfo.nickname) {
			this.loadUserStats()
		}
	},
	methods: {
		loadUserInfo() {
			// 从本地存储获取用户信息
			const userInfo = uni.getStorageSync('userInfo')
			if (userInfo) {
				this.userInfo = userInfo
			}
		},

		async loadUserStats() {
			if (!this.userInfo.nickname) return

			try {
				const result = await api.getUserStats()
				this.userStats = result.data
			} catch (error) {
				console.error('加载用户统计数据失败:', error)
			}
		},
		async login() {
			try {
				// 获取用户信息
				const userProfile = await new Promise((resolve, reject) => {
					uni.getUserProfile({
						desc: '用于完善用户资料',
						success: resolve,
						fail: reject
					})
				})

				// 获取登录凭证
				const loginCode = await new Promise((resolve, reject) => {
					uni.login({
						success: resolve,
						fail: reject
					})
				})

				// 调用登录 API
				const result = await api.wxLogin(loginCode.code, userProfile.userInfo)

				// 保存用户信息和 token
				this.userInfo = result.data.userInfo
				uni.setStorageSync('userInfo', this.userInfo)
				uni.setStorageSync('token', result.data.token)
				uni.setStorageSync('userId', result.data.userInfo.uid)

				// 加载用户统计数据
				this.loadUserStats()

				uni.showToast({
					title: '登录成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('登录失败:', error)
				uni.showToast({
					title: error.message || '登录失败',
					icon: 'none'
				})
			}
		},
		goToFavorites() {
			if (!this.checkLogin()) return
			uni.navigateTo({
				url: '/pages/my/favorites'
			})
		},
		goToMyPosts() {
			if (!this.checkLogin()) return
			uni.navigateTo({
				url: '/pages/my/my-posts'
			})
		},
		goToBrowseHistory() {
			if (!this.checkLogin()) return
			uni.navigateTo({
				url: '/pages/my/browse-history'
			})
		},
		bindPhone() {
			uni.showModal({
				title: '绑定手机号',
				content: '是否绑定手机号以便更好地为您服务？',
				success: (res) => {
					if (res.confirm) {
						// TODO: 实现手机号绑定功能
						uni.showToast({
							title: '功能开发中',
							icon: 'none'
						})
					}
				}
			})
		},
		showAbout() {
			uni.showModal({
				title: '关于我们',
				content: '房屋租赁小程序 v1.0.0\n为您提供便捷的租房服务',
				showCancel: false
			})
		},
		contactService() {
			uni.showActionSheet({
				itemList: ['拨打客服电话', '复制微信号'],
				success: (res) => {
					if (res.tapIndex === 0) {
						uni.makePhoneCall({
							phoneNumber: '************'
						})
					} else if (res.tapIndex === 1) {
						uni.setClipboardData({
							data: 'service_wechat',
							success: () => {
								uni.showToast({
									title: '微信号已复制',
									icon: 'success'
								})
							}
						})
					}
				}
			})
		},
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除用户信息
						this.userInfo = {
							nickname: '',
							avatar: '',
							phone: ''
						}
						this.userStats = {
							favorites: 0,
							posts: 0,
							views: 0
						}
						uni.removeStorageSync('userInfo')
						uni.removeStorageSync('token')
						uni.showToast({
							title: '已退出登录',
							icon: 'success'
						})
					}
				}
			})
		},
		checkLogin() {
			if (!this.userInfo.nickname) {
				uni.showModal({
					title: '请先登录',
					content: '需要登录后才能使用此功能',
					success: (res) => {
						if (res.confirm) {
							this.login()
						}
					}
				})
				return false
			}
			return true
		}
	}
}
</script>

<style lang="scss" scoped>
.my-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.user-info {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	padding: 60rpx 30rpx 40rpx;
	color: #fff;
}

.user-avatar {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.user-profile {
	display: flex;
	align-items: center;
}

.user-details {
	flex: 1;
}

.stats-section {
	background-color: #fff;
	margin: -20rpx 30rpx 20rpx;
	border-radius: 20rpx;
	padding: 40rpx 0;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.menu-section {
	background-color: #fff;
	margin: 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.version-info {
	text-align: center;
	padding: 40rpx;
}
</style>
