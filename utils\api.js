/**
 * 网络请求封装
 * 统一处理云函数调用和错误处理
 */

class API {
	constructor() {
		this.baseConfig = {
			timeout: 10000
		}
	}
	
	/**
	 * 调用云函数
	 * @param {string} name 云函数名称
	 * @param {object} data 请求参数
	 */
	async callFunction(name, data = {}) {
		try {
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
			
			const result = await uniCloud.callFunction({
				name: name,
				data: data
			})
			
			uni.hideLoading()
			
			if (result.result.code === 200) {
				return result.result
			} else {
				uni.showToast({
					title: result.result.message || '请求失败',
					icon: 'none'
				})
				throw new Error(result.result.message || '请求失败')
			}
		} catch (error) {
			uni.hideLoading()
			console.error('云函数调用失败:', error)
			uni.showToast({
				title: error.message || '网络错误',
				icon: 'none'
			})
			throw error
		}
	}
	
	// ==================== 房源相关API ====================
	
	/**
	 * 获取房源列表
	 * @param {object} params 查询参数
	 */
	async getHouseList(params = {}) {
		return await this.callFunction('house-api', {
			action: 'getHouseList',
			data: params
		})
	}
	
	/**
	 * 获取房源详情
	 * @param {string} houseId 房源ID
	 */
	async getHouseDetail(houseId) {
		return await this.callFunction('house-api', {
			action: 'getHouseDetail',
			data: { houseId }
		})
	}
	
	/**
	 * 发布房源
	 * @param {object} houseInfo 房源信息
	 */
	async postHouse(houseInfo) {
		const userId = uni.getStorageSync('userId')
		if (!userId) {
			throw new Error('请先登录')
		}
		
		return await this.callFunction('house-api', {
			action: 'postHouse',
			data: { houseInfo, userId }
		})
	}
	
	/**
	 * 更新房源
	 * @param {string} houseId 房源ID
	 * @param {object} houseInfo 房源信息
	 */
	async updateHouse(houseId, houseInfo) {
		const userId = uni.getStorageSync('userId')
		if (!userId) {
			throw new Error('请先登录')
		}
		
		return await this.callFunction('house-api', {
			action: 'updateHouse',
			data: { houseId, houseInfo, userId }
		})
	}
	
	/**
	 * 删除房源
	 * @param {string} houseId 房源ID
	 */
	async deleteHouse(houseId) {
		const userId = uni.getStorageSync('userId')
		if (!userId) {
			throw new Error('请先登录')
		}
		
		return await this.callFunction('house-api', {
			action: 'deleteHouse',
			data: { houseId, userId }
		})
	}
	
	/**
	 * 搜索房源
	 * @param {string} keyword 搜索关键词
	 * @param {object} params 其他参数
	 */
	async searchHouse(keyword, params = {}) {
		return await this.callFunction('house-api', {
			action: 'searchHouse',
			data: { keyword, ...params }
		})
	}
	
	/**
	 * 切换收藏状态
	 * @param {string} houseId 房源ID
	 */
	async toggleFavorite(houseId) {
		const userId = uni.getStorageSync('userId')
		if (!userId) {
			throw new Error('请先登录')
		}
		
		return await this.callFunction('house-api', {
			action: 'toggleFavorite',
			data: { houseId, userId }
		})
	}
	
	// ==================== 用户相关API ====================
	
	/**
	 * 微信登录
	 * @param {string} code 微信登录code
	 * @param {object} userInfo 用户信息
	 */
	async wxLogin(code, userInfo) {
		return await this.callFunction('user-api', {
			action: 'wxLogin',
			data: { code, userInfo }
		})
	}
	
	/**
	 * 获取用户信息
	 * @param {string} userId 用户ID
	 */
	async getUserInfo(userId) {
		return await this.callFunction('user-api', {
			action: 'getUserInfo',
			data: { userId }
		})
	}
	
	/**
	 * 更新用户信息
	 * @param {object} userInfo 用户信息
	 */
	async updateUserInfo(userInfo) {
		const userId = uni.getStorageSync('userId')
		if (!userId) {
			throw new Error('请先登录')
		}
		
		return await this.callFunction('user-api', {
			action: 'updateUserInfo',
			data: { userId, userInfo }
		})
	}
	
	/**
	 * 获取用户统计数据
	 */
	async getUserStats() {
		const userId = uni.getStorageSync('userId')
		if (!userId) {
			throw new Error('请先登录')
		}
		
		return await this.callFunction('user-api', {
			action: 'getUserStats',
			data: { userId }
		})
	}
	
	/**
	 * 获取用户收藏列表
	 * @param {object} params 查询参数
	 */
	async getFavorites(params = {}) {
		const userId = uni.getStorageSync('userId')
		if (!userId) {
			throw new Error('请先登录')
		}
		
		return await this.callFunction('user-api', {
			action: 'getFavorites',
			data: { userId, ...params }
		})
	}
	
	/**
	 * 获取用户发布的房源
	 * @param {object} params 查询参数
	 */
	async getMyPosts(params = {}) {
		const userId = uni.getStorageSync('userId')
		if (!userId) {
			throw new Error('请先登录')
		}
		
		return await this.callFunction('user-api', {
			action: 'getMyPosts',
			data: { userId, ...params }
		})
	}
	
	// ==================== 文件上传相关 ====================
	
	/**
	 * 上传图片到云存储
	 * @param {string} filePath 本地文件路径
	 * @param {string} cloudPath 云存储路径
	 */
	async uploadImage(filePath, cloudPath) {
		try {
			const result = await uniCloud.uploadFile({
				filePath: filePath,
				cloudPath: cloudPath
			})
			
			return {
				code: 200,
				data: {
					fileID: result.fileID,
					tempFileURL: result.tempFileURL
				}
			}
		} catch (error) {
			console.error('图片上传失败:', error)
			throw new Error('图片上传失败')
		}
	}
	
	/**
	 * 批量上传图片
	 * @param {Array} filePaths 本地文件路径数组
	 * @param {string} folder 云存储文件夹
	 */
	async uploadImages(filePaths, folder = 'house') {
		const uploadPromises = filePaths.map((filePath, index) => {
			const timestamp = Date.now()
			const random = Math.random().toString(36).substr(2, 9)
			const cloudPath = `${folder}/${timestamp}_${random}_${index}.jpg`
			return this.uploadImage(filePath, cloudPath)
		})
		
		try {
			const results = await Promise.all(uploadPromises)
			return {
				code: 200,
				data: results.map(result => result.data.tempFileURL)
			}
		} catch (error) {
			console.error('批量上传失败:', error)
			throw new Error('图片上传失败')
		}
	}
}

// 创建API实例
const api = new API()

export default api
