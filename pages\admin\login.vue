<template>
	<view class="admin-login-container">
		<view class="login-card">
			<view class="login-header">
				<u-icon name="shield-checkmark" size="80" color="#007AFF"></u-icon>
				<u-text text="管理后台" size="36" bold color="#333" style="margin-top: 20rpx;"></u-text>
				<u-text text="房屋租赁小程序管理系统" size="26" color="#666" style="margin-top: 10rpx;"></u-text>
			</view>
			
			<view class="login-form">
				<u-form :model="loginForm" ref="loginForm" :rules="rules">
					<u-form-item prop="username" border-bottom>
						<u-input 
							v-model="loginForm.username"
							placeholder="请输入管理员账号"
							prefix-icon="account"
							clearable
						></u-input>
					</u-form-item>
					
					<u-form-item prop="password" border-bottom>
						<u-input 
							v-model="loginForm.password"
							placeholder="请输入密码"
							type="password"
							prefix-icon="lock"
							clearable
						></u-input>
					</u-form-item>
				</u-form>
				
				<view class="login-options">
					<u-checkbox v-model="rememberMe" size="28">记住密码</u-checkbox>
				</view>
				
				<u-button 
					text="登录"
					type="primary"
					size="large"
					:loading="logging"
					@click="handleLogin"
					style="margin-top: 40rpx;"
				></u-button>
			</view>
			
			<view class="login-footer">
				<u-text text="© 2024 房屋租赁小程序" size="24" color="#999"></u-text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loginForm: {
				username: '',
				password: ''
			},
			rules: {
				username: [
					{
						required: true,
						message: '请输入管理员账号',
						trigger: 'blur'
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: 'blur'
					},
					{
						min: 6,
						message: '密码长度不能少于6位',
						trigger: 'blur'
					}
				]
			},
			rememberMe: false,
			logging: false
		}
	},
	onLoad() {
		this.loadSavedCredentials()
	},
	methods: {
		loadSavedCredentials() {
			const savedCredentials = uni.getStorageSync('adminCredentials')
			if (savedCredentials) {
				this.loginForm = savedCredentials
				this.rememberMe = true
			}
		},
		
		async handleLogin() {
			try {
				const valid = await this.$refs.loginForm.validate()
				if (!valid) return
				
				this.logging = true
				
				// TODO: 调用管理员登录API
				await this.mockLogin()
				
				// 保存登录凭据
				if (this.rememberMe) {
					uni.setStorageSync('adminCredentials', this.loginForm)
				} else {
					uni.removeStorageSync('adminCredentials')
				}
				
				// 保存登录状态
				uni.setStorageSync('adminToken', 'mock_admin_token')
				uni.setStorageSync('adminInfo', {
					username: this.loginForm.username,
					loginTime: new Date().toISOString()
				})
				
				uni.showToast({
					title: '登录成功',
					icon: 'success'
				})
				
				// 跳转到管理后台首页
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/admin/dashboard'
					})
				}, 1500)
				
			} catch (error) {
				console.error('登录失败:', error)
				uni.showToast({
					title: error.message || '登录失败',
					icon: 'none'
				})
			} finally {
				this.logging = false
			}
		},
		
		async mockLogin() {
			// 模拟登录验证
			return new Promise((resolve, reject) => {
				setTimeout(() => {
					if (this.loginForm.username === 'admin' && this.loginForm.password === '123456') {
						resolve()
					} else {
						reject(new Error('用户名或密码错误'))
					}
				}, 1000)
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.admin-login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.login-card {
	width: 100%;
	max-width: 600rpx;
	background-color: #fff;
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.login-header {
	text-align: center;
	margin-bottom: 60rpx;
}

.login-form {
	margin-bottom: 40rpx;
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 30rpx;
}

.login-footer {
	text-align: center;
	margin-top: 40rpx;
}
</style>
