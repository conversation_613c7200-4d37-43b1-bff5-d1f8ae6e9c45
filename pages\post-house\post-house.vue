<template>
	<view class="post-house-container">
		<form @submit="submitHouse">
			<!-- 房源图片 -->
			<view class="form-section">
				<view class="section-title">房源图片 *</view>
				<view class="image-upload">
					<view class="image-item" v-for="(image, index) in houseForm.images" :key="index">
						<image :src="image" mode="aspectFill" class="uploaded-image"></image>
						<view class="image-delete" @click="deleteImage(index)">✕</view>
					</view>
					<view class="upload-btn" @click="chooseImage" v-if="houseForm.images.length < 9">
						<text class="upload-icon">📷</text>
						<text class="upload-text">添加图片</text>
						<text class="upload-tip">{{ houseForm.images.length }}/9</text>
					</view>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>
				<view class="form-item">
					<text class="form-label">房源标题 *</text>
					<input class="form-input" v-model="houseForm.title" placeholder="请输入房源标题" maxlength="50" />
				</view>
				<view class="form-item">
					<text class="form-label">租金 *</text>
					<view class="price-input">
						<input class="form-input" v-model="houseForm.price" placeholder="请输入租金" type="number" />
						<text class="price-unit">元/月</text>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">户型 *</text>
					<picker @change="onTypeChange" :value="typeIndex" :range="typeOptions">
						<view class="picker-input">
							<text :class="{ placeholder: !houseForm.type }">{{ houseForm.type || '请选择户型' }}</text>
							<text class="picker-arrow">▼</text>
						</view>
					</picker>
				</view>
			</view>
			
			<!-- 位置信息 -->
			<view class="form-section">
				<view class="section-title">位置信息</view>
				<view class="form-item">
					<text class="form-label">详细地址 *</text>
					<view class="location-input" @click="chooseLocation">
						<text :class="{ placeholder: !houseForm.location.address }">
							{{ houseForm.location.address || '点击选择位置' }}
						</text>
						<text class="location-icon">📍</text>
					</view>
				</view>
			</view>
			
			<!-- 房源配置 -->
			<view class="form-section">
				<view class="section-title">房源配置</view>
				<view class="config-grid">
					<view class="config-item" 
						  v-for="(config, index) in configOptions" 
						  :key="index"
						  :class="{ active: houseForm.config.includes(config) }"
						  @click="toggleConfig(config)">
						<text>{{ config }}</text>
					</view>
				</view>
			</view>
			
			<!-- 房源描述 -->
			<view class="form-section">
				<view class="section-title">房源描述</view>
				<textarea class="form-textarea" 
						  v-model="houseForm.desc" 
						  placeholder="请详细描述房源情况，如周边环境、交通便利性等"
						  maxlength="500"></textarea>
			</view>
			
			<!-- 联系方式 -->
			<view class="form-section">
				<view class="section-title">联系方式</view>
				<view class="form-item">
					<text class="form-label">联系电话</text>
					<input class="form-input" v-model="houseForm.contact.phone" placeholder="请输入联系电话" type="number" />
				</view>
				<view class="form-item">
					<text class="form-label">微信号</text>
					<input class="form-input" v-model="houseForm.contact.wechat" placeholder="请输入微信号" />
				</view>
			</view>
			
			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitHouse" :disabled="submitting">
					{{ submitting ? '发布中...' : '发布房源' }}
				</button>
				<view class="submit-tip">
					<text>发布后需要管理员审核，审核通过后才会显示</text>
				</view>
			</view>
		</form>
	</view>
</template>

<script>
export default {
	data() {
		return {
			houseForm: {
				title: '',
				price: '',
				type: '',
				location: {
					address: '',
					latitude: '',
					longitude: ''
				},
				config: [],
				desc: '',
				contact: {
					phone: '',
					wechat: ''
				},
				images: []
			},
			typeOptions: ['一居室', '二居室', '三居室', '四居室', '合租'],
			typeIndex: -1,
			configOptions: [
				'空调', '洗衣机', '冰箱', '热水器', '电视', 'WiFi',
				'床', '衣柜', '桌椅', '独立卫生间', '阳台', '电梯'
			],
			submitting: false
		}
	},
	methods: {
		chooseImage() {
			const remainCount = 9 - this.houseForm.images.length
			uni.chooseImage({
				count: remainCount,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadImages(res.tempFilePaths)
				}
			})
		},
		uploadImages(filePaths) {
			uni.showLoading({
				title: '上传中...'
			})
			
			// TODO: 调用云函数上传图片到云存储
			// 这里先模拟上传成功
			setTimeout(() => {
				this.houseForm.images.push(...filePaths)
				uni.hideLoading()
				uni.showToast({
					title: '上传成功',
					icon: 'success'
				})
			}, 1000)
		},
		deleteImage(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这张图片吗？',
				success: (res) => {
					if (res.confirm) {
						this.houseForm.images.splice(index, 1)
					}
				}
			})
		},
		onTypeChange(e) {
			this.typeIndex = e.detail.value
			this.houseForm.type = this.typeOptions[e.detail.value]
		},
		chooseLocation() {
			uni.chooseLocation({
				success: (res) => {
					this.houseForm.location = {
						address: res.address,
						latitude: res.latitude,
						longitude: res.longitude
					}
				},
				fail: (err) => {
					console.error('选择位置失败:', err)
					uni.showToast({
						title: '获取位置失败',
						icon: 'none'
					})
				}
			})
		},
		toggleConfig(config) {
			const index = this.houseForm.config.indexOf(config)
			if (index > -1) {
				this.houseForm.config.splice(index, 1)
			} else {
				this.houseForm.config.push(config)
			}
		},
		validateForm() {
			if (this.houseForm.images.length === 0) {
				uni.showToast({
					title: '请上传房源图片',
					icon: 'none'
				})
				return false
			}
			
			if (!this.houseForm.title.trim()) {
				uni.showToast({
					title: '请输入房源标题',
					icon: 'none'
				})
				return false
			}
			
			if (!this.houseForm.price || this.houseForm.price <= 0) {
				uni.showToast({
					title: '请输入正确的租金',
					icon: 'none'
				})
				return false
			}
			
			if (!this.houseForm.type) {
				uni.showToast({
					title: '请选择户型',
					icon: 'none'
				})
				return false
			}
			
			if (!this.houseForm.location.address) {
				uni.showToast({
					title: '请选择位置',
					icon: 'none'
				})
				return false
			}
			
			if (!this.houseForm.contact.phone && !this.houseForm.contact.wechat) {
				uni.showToast({
					title: '请至少填写一种联系方式',
					icon: 'none'
				})
				return false
			}
			
			return true
		},
		submitHouse() {
			if (!this.validateForm()) {
				return
			}
			
			this.submitting = true
			
			// TODO: 调用云函数提交房源信息
			setTimeout(() => {
				this.submitting = false
				uni.showModal({
					title: '发布成功',
					content: '房源已提交，等待管理员审核',
					showCancel: false,
					success: () => {
						// 重置表单
						this.resetForm()
						// 跳转到我的发布页面
						uni.switchTab({
							url: '/pages/my/my'
						})
					}
				})
			}, 2000)
		},
		resetForm() {
			this.houseForm = {
				title: '',
				price: '',
				type: '',
				location: {
					address: '',
					latitude: '',
					longitude: ''
				},
				config: [],
				desc: '',
				contact: {
					phone: '',
					wechat: ''
				},
				images: []
			}
			this.typeIndex = -1
		}
	}
}
</script>

<style scoped>
.post-house-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

.form-section {
	background-color: #fff;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.image-upload {
	display: flex;
	flex-wrap: wrap;
}

.image-item {
	position: relative;
	width: 200rpx;
	height: 200rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
}

.uploaded-image {
	width: 100%;
	height: 100%;
	border-radius: 10rpx;
}

.image-delete {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background-color: #ff4757;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}

.upload-btn {
	width: 200rpx;
	height: 200rpx;
	border: 2rpx dashed #ddd;
	border-radius: 10rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #999;
}

.upload-icon {
	font-size: 48rpx;
	margin-bottom: 10rpx;
}

.upload-text {
	font-size: 24rpx;
	margin-bottom: 5rpx;
}

.upload-tip {
	font-size: 20rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
}

.form-input {
	width: 100%;
	padding: 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	font-size: 28rpx;
	background-color: #fff;
}

.price-input {
	display: flex;
	align-items: center;
}

.price-input .form-input {
	flex: 1;
	margin-right: 20rpx;
}

.price-unit {
	font-size: 28rpx;
	color: #666;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	background-color: #fff;
}

.picker-input .placeholder {
	color: #999;
}

.picker-arrow {
	color: #999;
	font-size: 24rpx;
}

.location-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	background-color: #fff;
}

.location-input .placeholder {
	color: #999;
}

.location-icon {
	font-size: 32rpx;
	color: #007AFF;
}

.config-grid {
	display: flex;
	flex-wrap: wrap;
}

.config-item {
	padding: 15rpx 30rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
	background-color: #fff;
}

.config-item.active {
	background-color: #007AFF;
	color: #fff;
	border-color: #007AFF;
}

.form-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	font-size: 28rpx;
	background-color: #fff;
}

.submit-section {
	padding: 30rpx;
}

.submit-btn {
	width: 100%;
	padding: 30rpx;
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 10rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.submit-btn[disabled] {
	background-color: #ccc;
}

.submit-tip {
	text-align: center;
	margin-top: 20rpx;
	color: #999;
	font-size: 24rpx;
}
</style>
