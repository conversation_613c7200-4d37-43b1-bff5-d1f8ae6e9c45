<template>
	<view class="house-detail-container">
		<!-- 图片轮播 -->
		<u-swiper
			:list="swiperList"
			:autoplay="false"
			indicator
			height="500"
			border-radius="0"
			@click="previewImage"
		></u-swiper>
		
		<!-- 房源基本信息 -->
		<view class="basic-info">
			<view class="price-section">
				<view class="price-info">
					<u-text
						:text="`¥${houseInfo.price}`"
						size="48"
						color="#ff6b35"
						bold
					></u-text>
					<u-text
						text="/月"
						size="28"
						color="#ff6b35"
						style="margin-left: 10rpx;"
					></u-text>
				</view>
				<u-icon
					:name="isFavorite ? 'heart-fill' : 'heart'"
					:color="isFavorite ? '#ff6b35' : '#999'"
					size="48"
					@click="toggleFavorite"
				></u-icon>
			</view>

			<u-text
				:text="houseInfo.title"
				size="36"
				bold
				color="#333"
				style="margin: 20rpx 0;"
			></u-text>

			<view class="address-section">
				<u-icon name="map" color="#666" size="28"></u-icon>
				<u-text
					:text="houseInfo.location?.address || '位置未知'"
					size="28"
					color="#666"
					style="margin-left: 10rpx;"
				></u-text>
			</view>

			<view class="house-specs">
				<view class="spec-item">
					<u-text text="户型" size="24" color="#999"></u-text>
					<u-text :text="houseInfo.type" size="28" color="#333" bold></u-text>
				</view>
				<view class="spec-item">
					<u-text text="楼层" size="24" color="#999"></u-text>
					<u-text :text="houseInfo.floor || '未知'" size="28" color="#333" bold></u-text>
				</view>
				<view class="spec-item">
					<u-text text="面积" size="24" color="#999"></u-text>
					<u-text :text="`${houseInfo.area || '未知'}㎡`" size="28" color="#333" bold></u-text>
				</view>
			</view>
		</view>
		
		<!-- 房源配置 -->
		<view class="config-section">
			<u-text text="房源配置" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<view class="config-tags">
				<u-tag
					v-for="(config, index) in houseInfo.config"
					:key="index"
					:text="config"
					type="primary"
					plain
					size="default"
					style="margin-right: 20rpx; margin-bottom: 15rpx;"
				></u-tag>
			</view>
		</view>

		<!-- 房源描述 -->
		<view class="desc-section">
			<u-text text="房源描述" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<u-text
				:text="houseInfo.desc"
				size="28"
				color="#666"
				style="line-height: 1.6;"
			></u-text>
		</view>
		
		<!-- 房东信息 -->
		<view class="owner-section">
			<u-text text="房东信息" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<view class="owner-info">
				<u-avatar
					:src="ownerInfo.avatar"
					size="80"
					style="margin-right: 20rpx;"
				></u-avatar>
				<view class="owner-details">
					<u-text
						:text="ownerInfo.nickname"
						size="30"
						bold
						color="#333"
						style="margin-bottom: 10rpx;"
					></u-text>
					<u-text
						text="房东很nice，有问题随时联系"
						size="26"
						color="#666"
					></u-text>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<u-button
				text="电话"
				type="info"
				plain
				size="default"
				icon="phone"
				@click="callOwner"
				style="flex: 1; margin-right: 20rpx;"
			></u-button>
			<u-button
				text="微信"
				type="success"
				plain
				size="default"
				icon="chat"
				@click="contactWechat"
				style="flex: 1; margin-right: 20rpx;"
			></u-button>
			<u-button
				text="联系房东"
				type="primary"
				size="default"
				@click="showContactModal"
				style="flex: 2;"
			></u-button>
		</view>
		
		<!-- 联系方式弹窗 -->
		<u-popup
			:show="showContact"
			mode="center"
			border-radius="20"
			@close="hideContactModal"
		>
			<view class="popup-content">
				<view class="popup-header">
					<u-text text="联系房东" size="32" bold color="#333"></u-text>
					<u-icon name="close" size="32" color="#999" @click="hideContactModal"></u-icon>
				</view>
				<view class="contact-info">
					<view class="contact-item" v-if="houseInfo.contact?.phone">
						<u-text text="电话：" size="28" color="#666" style="width: 100rpx;"></u-text>
						<u-text
							:text="houseInfo.contact.phone"
							size="28"
							color="#007AFF"
							@click="callPhone(houseInfo.contact.phone)"
						></u-text>
					</view>
					<view class="contact-item" v-if="houseInfo.contact?.wechat">
						<u-text text="微信：" size="28" color="#666" style="width: 100rpx;"></u-text>
						<u-text
							:text="houseInfo.contact.wechat"
							size="28"
							color="#007AFF"
							@click="copyWechat(houseInfo.contact.wechat)"
						></u-text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 举报按钮 -->
		<view class="report-btn">
			<u-button
				text="举报"
				type="error"
				plain
				size="mini"
				@click="reportHouse"
				icon="error-circle"
			></u-button>
		</view>
	</view>
</template>

<script>
import api from '@/utils/api.js'

export default {
	data() {
		return {
			houseId: '',
			houseInfo: {
				images: [],
				title: '',
				price: 0,
				location: {},
				type: '',
				config: [],
				desc: '',
				contact: {}
			},
			ownerInfo: {
				avatar: '/static/default-avatar.png',
				nickname: '房东'
			},
			isFavorite: false,
			showContact: false,
			loading: false
		}
	},
	computed: {
		swiperList() {
			return this.houseInfo.images?.map(image => ({ image })) || []
		}
	},
	onLoad(options) {
		this.houseId = options.id
		this.loadHouseDetail()
	},
	methods: {
		async loadHouseDetail() {
			try {
				this.loading = true
				const result = await api.getHouseDetail(this.houseId)
				this.houseInfo = result.data
				this.checkFavoriteStatus()
			} catch (error) {
				console.error('加载房源详情失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		previewImage(index) {
			if (this.houseInfo.images && this.houseInfo.images.length > 0) {
				uni.previewImage({
					urls: this.houseInfo.images,
					current: index || 0
				})
			}
		},

		checkFavoriteStatus() {
			// TODO: 检查收藏状态
			this.isFavorite = false
		},
		async toggleFavorite() {
			try {
				const result = await api.toggleFavorite(this.houseId)
				this.isFavorite = result.data.isFavorite
				uni.showToast({
					title: result.message,
					icon: 'success'
				})
			} catch (error) {
				console.error('切换收藏状态失败:', error)
			}
		},
		callOwner() {
			if (this.houseInfo.contact?.phone) {
				this.callPhone(this.houseInfo.contact.phone)
			} else {
				uni.showToast({
					title: '暂无电话信息',
					icon: 'none'
				})
			}
		},
		callPhone(phone) {
			uni.makePhoneCall({
				phoneNumber: phone,
				fail: (err) => {
					console.error('拨打电话失败:', err)
				}
			})
		},
		contactWechat() {
			this.showContactModal()
		},

		copyWechat(wechat) {
			uni.setClipboardData({
				data: wechat,
				success: () => {
					uni.showToast({
						title: '微信号已复制',
						icon: 'success'
					})
					this.hideContactModal()
				}
			})
		},
		showContactModal() {
			this.showContact = true
		},
		hideContactModal() {
			this.showContact = false
		},
		reportHouse() {
			uni.showModal({
				title: '举报房源',
				content: '确定要举报这个房源吗？',
				success: (res) => {
					if (res.confirm) {
						// TODO: 调用云函数举报房源
						uni.showToast({
							title: '举报成功',
							icon: 'success'
						})
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.house-detail-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.basic-info {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.price-section {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.price-info {
	display: flex;
	align-items: baseline;
}

.address-section {
	display: flex;
	align-items: center;
	margin: 20rpx 0 30rpx;
}

.house-specs {
	display: flex;
	justify-content: space-between;
	margin-top: 30rpx;
}

.spec-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 10rpx;
	margin: 0 10rpx;

	&:first-child {
		margin-left: 0;
	}

	&:last-child {
		margin-right: 0;
	}
}

.config-section, .desc-section, .owner-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border-radius: 20rpx;
	margin: 20rpx;
}

.config-tags {
	display: flex;
	flex-wrap: wrap;
}

.owner-info {
	display: flex;
	align-items: center;
}

.owner-details {
	flex: 1;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	background-color: #fff;
	padding: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.popup-content {
	width: 600rpx;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.contact-info {
	padding: 20rpx 0;
}

.contact-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 10rpx;
}

.report-btn {
	position: fixed;
	top: 100rpx;
	right: 30rpx;
	z-index: 100;
}
</style>
