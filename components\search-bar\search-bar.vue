<template>
	<view class="search-bar">
		<u-search 
			:placeholder="placeholder"
			:value="keyword"
			:show-action="showAction"
			:action-text="actionText"
			:disabled="disabled"
			:focus="focus"
			:clear-trigger="clearTrigger"
			:max-length="maxLength"
			@search="onSearch"
			@input="onInput"
			@focus="onFocus"
			@blur="onBlur"
			@clear="onClear"
			@click="onClick"
			@custom="onAction"
		>
			<template #prefix>
				<u-icon 
					name="search" 
					size="32" 
					color="#999"
					style="margin-right: 10rpx;"
				></u-icon>
			</template>
		</u-search>
	</view>
</template>

<script>
export default {
	name: 'SearchBar',
	props: {
		// 搜索关键词
		value: {
			type: String,
			default: ''
		},
		// 占位符文字
		placeholder: {
			type: String,
			default: '搜索房源、地址、关键词'
		},
		// 是否显示右侧操作按钮
		showAction: {
			type: <PERSON>olean,
			default: false
		},
		// 右侧操作按钮文字
		actionText: {
			type: String,
			default: '搜索'
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false
		},
		// 是否自动聚焦
		focus: {
			type: Boolean,
			default: false
		},
		// 清除触发器
		clearTrigger: {
			type: String,
			default: 'always'
		},
		// 最大输入长度
		maxLength: {
			type: [String, Number],
			default: 50
		}
	},
	data() {
		return {
			keyword: this.value
		}
	},
	watch: {
		value(newVal) {
			this.keyword = newVal
		},
		keyword(newVal) {
			this.$emit('input', newVal)
		}
	},
	methods: {
		// 搜索事件
		onSearch(keyword) {
			this.$emit('search', keyword)
		},
		
		// 输入事件
		onInput(keyword) {
			this.keyword = keyword
			this.$emit('input', keyword)
		},
		
		// 聚焦事件
		onFocus() {
			this.$emit('focus')
		},
		
		// 失焦事件
		onBlur() {
			this.$emit('blur')
		},
		
		// 清除事件
		onClear() {
			this.keyword = ''
			this.$emit('clear')
			this.$emit('input', '')
		},
		
		// 点击事件
		onClick() {
			this.$emit('click')
		},
		
		// 右侧按钮点击事件
		onAction() {
			this.$emit('action', this.keyword)
		}
	}
}
</script>

<style lang="scss" scoped>
.search-bar {
	padding: 20rpx;
	background-color: #fff;
}
</style>
