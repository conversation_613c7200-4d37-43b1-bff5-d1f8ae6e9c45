<template>
	<view class="admin-dashboard">
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left">
				<u-text text="管理后台" size="32" bold color="#333"></u-text>
			</view>
			<view class="nav-right">
				<u-button 
					text="退出"
					type="error"
					plain
					size="mini"
					@click="logout"
				></u-button>
			</view>
		</view>
		
		<!-- 统计卡片 -->
		<view class="stats-grid">
			<view class="stat-card" v-for="(stat, index) in statsData" :key="index">
				<view class="stat-icon">
					<u-icon :name="stat.icon" :color="stat.color" size="48"></u-icon>
				</view>
				<view class="stat-info">
					<u-text :text="stat.value" size="36" bold :color="stat.color"></u-text>
					<u-text :text="stat.label" size="24" color="#666" style="margin-top: 10rpx;"></u-text>
				</view>
			</view>
		</view>
		
		<!-- 快捷操作 -->
		<view class="quick-actions">
			<u-text text="快捷操作" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<u-grid :col="2" :border="false">
				<u-grid-item v-for="(action, index) in quickActions" :key="index" @click="handleQuickAction(action)">
					<view class="action-item">
						<u-icon :name="action.icon" :color="action.color" size="48"></u-icon>
						<u-text :text="action.label" size="28" color="#333" style="margin-top: 15rpx;"></u-text>
					</view>
				</u-grid-item>
			</u-grid>
		</view>
		
		<!-- 最新房源 -->
		<view class="recent-houses">
			<view class="section-header">
				<u-text text="最新房源" size="32" bold color="#333"></u-text>
				<u-text text="查看全部 >" size="26" color="#007AFF" @click="goToHouseManage"></u-text>
			</view>
			
			<view class="house-list">
				<view class="house-item" v-for="(house, index) in recentHouses" :key="index">
					<u-image 
						:src="house.images[0]" 
						width="120rpx" 
						height="90rpx" 
						border-radius="10rpx"
						style="margin-right: 20rpx;"
					></u-image>
					<view class="house-info">
						<u-text :text="house.title" size="28" color="#333" lines="1"></u-text>
						<u-text :text="`¥${house.price}/月`" size="24" color="#ff6b35" style="margin-top: 5rpx;"></u-text>
					</view>
					<view class="house-status">
						<u-tag 
							:text="getStatusText(house.status)"
							:type="getStatusType(house.status)"
							size="mini"
						></u-tag>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 系统信息 -->
		<view class="system-info">
			<u-text text="系统信息" size="32" bold color="#333" style="margin-bottom: 30rpx;"></u-text>
			<view class="info-list">
				<view class="info-item">
					<u-text text="系统版本" size="28" color="#666"></u-text>
					<u-text text="v1.0.0" size="28" color="#333"></u-text>
				</view>
				<view class="info-item">
					<u-text text="最后登录" size="28" color="#666"></u-text>
					<u-text :text="lastLoginTime" size="28" color="#333"></u-text>
				</view>
				<view class="info-item">
					<u-text text="在线用户" size="28" color="#666"></u-text>
					<u-text text="12" size="28" color="#333"></u-text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statsData: [
				{
					label: '总房源数',
					value: '156',
					icon: 'home',
					color: '#007AFF'
				},
				{
					label: '待审核',
					value: '8',
					icon: 'clock',
					color: '#ff9500'
				},
				{
					label: '用户总数',
					value: '1,234',
					icon: 'account',
					color: '#19be6b'
				},
				{
					label: '今日访问',
					value: '89',
					icon: 'eye',
					color: '#ff6b35'
				}
			],
			quickActions: [
				{
					label: '房源管理',
					icon: 'home',
					color: '#007AFF',
					action: 'house-manage'
				},
				{
					label: '用户管理',
					icon: 'account',
					color: '#19be6b',
					action: 'user-manage'
				},
				{
					label: '审核房源',
					icon: 'checkmark-circle',
					color: '#ff9500',
					action: 'house-audit'
				},
				{
					label: '系统设置',
					icon: 'setting',
					color: '#909399',
					action: 'settings'
				}
			],
			recentHouses: [],
			lastLoginTime: ''
		}
	},
	onLoad() {
		this.checkAdminAuth()
		this.loadDashboardData()
	},
	methods: {
		checkAdminAuth() {
			const adminToken = uni.getStorageSync('adminToken')
			if (!adminToken) {
				uni.redirectTo({
					url: '/pages/admin/login'
				})
				return
			}
			
			const adminInfo = uni.getStorageSync('adminInfo')
			if (adminInfo && adminInfo.loginTime) {
				this.lastLoginTime = new Date(adminInfo.loginTime).toLocaleString()
			}
		},
		
		async loadDashboardData() {
			try {
				// TODO: 调用API获取仪表板数据
				this.loadMockData()
			} catch (error) {
				console.error('加载仪表板数据失败:', error)
			}
		},
		
		loadMockData() {
			// 模拟最新房源数据
			this.recentHouses = [
				{
					title: '精装两居室，拎包入住',
					price: 3500,
					images: ['/static/placeholder.jpg'],
					status: 'pending'
				},
				{
					title: '温馨一居室，交通便利',
					price: 2800,
					images: ['/static/placeholder.jpg'],
					status: 'approved'
				},
				{
					title: '豪华三居室，设施齐全',
					price: 5200,
					images: ['/static/placeholder.jpg'],
					status: 'rejected'
				}
			]
		},
		
		handleQuickAction(action) {
			switch (action.action) {
				case 'house-manage':
					this.goToHouseManage()
					break
				case 'user-manage':
					this.goToUserManage()
					break
				case 'house-audit':
					this.goToHouseAudit()
					break
				case 'settings':
					this.goToSettings()
					break
			}
		},
		
		goToHouseManage() {
			uni.navigateTo({
				url: '/pages/admin/house-manage'
			})
		},
		
		goToUserManage() {
			uni.navigateTo({
				url: '/pages/admin/user-manage'
			})
		},
		
		goToHouseAudit() {
			uni.navigateTo({
				url: '/pages/admin/house-audit'
			})
		},
		
		goToSettings() {
			uni.navigateTo({
				url: '/pages/admin/settings'
			})
		},
		
		getStatusText(status) {
			const statusMap = {
				pending: '待审核',
				approved: '已通过',
				rejected: '已驳回'
			}
			return statusMap[status] || '未知'
		},
		
		getStatusType(status) {
			const typeMap = {
				pending: 'warning',
				approved: 'success',
				rejected: 'error'
			}
			return typeMap[status] || 'info'
		},
		
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出管理后台吗？',
				success: (res) => {
					if (res.confirm) {
						uni.removeStorageSync('adminToken')
						uni.removeStorageSync('adminInfo')
						uni.redirectTo({
							url: '/pages/admin/login'
						})
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.admin-dashboard {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
}

.top-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #fff;
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.stat-card {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
}

.stat-icon {
	margin-right: 20rpx;
}

.stat-info {
	flex: 1;
}

.quick-actions {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx;
}

.recent-houses {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.house-list {
	display: flex;
	flex-direction: column;
}

.house-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.house-info {
	flex: 1;
	margin-right: 20rpx;
}

.house-status {
	flex-shrink: 0;
}

.system-info {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 20rpx;
}

.info-list {
	display: flex;
	flex-direction: column;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}
</style>
