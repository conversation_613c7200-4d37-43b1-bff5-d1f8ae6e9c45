/**
 * 工具函数集合
 */

/**
 * 格式化时间
 * @param {Date|string|number} date 时间
 * @param {string} format 格式化模板
 */
export function formatTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
	if (!date) return ''
	
	const d = new Date(date)
	if (isNaN(d.getTime())) return ''
	
	const year = d.getFullYear()
	const month = String(d.getMonth() + 1).padStart(2, '0')
	const day = String(d.getDate()).padStart(2, '0')
	const hour = String(d.getHours()).padStart(2, '0')
	const minute = String(d.getMinutes()).padStart(2, '0')
	const second = String(d.getSeconds()).padStart(2, '0')
	
	return format
		.replace('YYYY', year)
		.replace('MM', month)
		.replace('DD', day)
		.replace('HH', hour)
		.replace('mm', minute)
		.replace('ss', second)
}

/**
 * 格式化相对时间
 * @param {Date|string|number} date 时间
 */
export function formatRelativeTime(date) {
	if (!date) return ''
	
	const d = new Date(date)
	if (isNaN(d.getTime())) return ''
	
	const now = new Date()
	const diff = now.getTime() - d.getTime()
	
	const minute = 60 * 1000
	const hour = 60 * minute
	const day = 24 * hour
	const month = 30 * day
	const year = 365 * day
	
	if (diff < minute) {
		return '刚刚'
	} else if (diff < hour) {
		return Math.floor(diff / minute) + '分钟前'
	} else if (diff < day) {
		return Math.floor(diff / hour) + '小时前'
	} else if (diff < month) {
		return Math.floor(diff / day) + '天前'
	} else if (diff < year) {
		return Math.floor(diff / month) + '个月前'
	} else {
		return Math.floor(diff / year) + '年前'
	}
}

/**
 * 格式化价格
 * @param {number} price 价格
 * @param {string} unit 单位
 */
export function formatPrice(price, unit = '元') {
	if (typeof price !== 'number') return '面议'
	
	if (price >= 10000) {
		return (price / 10000).toFixed(1) + '万' + unit
	} else if (price >= 1000) {
		return (price / 1000).toFixed(1) + 'k' + unit
	} else {
		return price + unit
	}
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间
 */
export function debounce(func, delay = 300) {
	let timer = null
	return function(...args) {
		if (timer) clearTimeout(timer)
		timer = setTimeout(() => {
			func.apply(this, args)
		}, delay)
	}
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间
 */
export function throttle(func, delay = 300) {
	let timer = null
	return function(...args) {
		if (!timer) {
			timer = setTimeout(() => {
				func.apply(this, args)
				timer = null
			}, delay)
		}
	}
}

/**
 * 深拷贝
 * @param {any} obj 要拷贝的对象
 */
export function deepClone(obj) {
	if (obj === null || typeof obj !== 'object') return obj
	if (obj instanceof Date) return new Date(obj)
	if (obj instanceof Array) return obj.map(item => deepClone(item))
	if (typeof obj === 'object') {
		const clonedObj = {}
		for (let key in obj) {
			if (obj.hasOwnProperty(key)) {
				clonedObj[key] = deepClone(obj[key])
			}
		}
		return clonedObj
	}
}

/**
 * 生成唯一ID
 */
export function generateId() {
	return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 验证手机号
 * @param {string} phone 手机号
 */
export function validatePhone(phone) {
	const reg = /^1[3-9]\d{9}$/
	return reg.test(phone)
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 */
export function validateEmail(email) {
	const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
	return reg.test(email)
}

/**
 * 获取图片信息
 * @param {string} src 图片路径
 */
export function getImageInfo(src) {
	return new Promise((resolve, reject) => {
		uni.getImageInfo({
			src: src,
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 压缩图片
 * @param {string} src 图片路径
 * @param {number} quality 压缩质量 0-1
 */
export function compressImage(src, quality = 0.8) {
	return new Promise((resolve, reject) => {
		uni.compressImage({
			src: src,
			quality: quality,
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 获取当前位置
 */
export function getCurrentLocation() {
	return new Promise((resolve, reject) => {
		uni.getLocation({
			type: 'gcj02',
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 计算两点间距离（单位：米）
 * @param {number} lat1 纬度1
 * @param {number} lng1 经度1
 * @param {number} lat2 纬度2
 * @param {number} lng2 经度2
 */
export function calculateDistance(lat1, lng1, lat2, lng2) {
	const R = 6371000 // 地球半径，单位米
	const dLat = (lat2 - lat1) * Math.PI / 180
	const dLng = (lng2 - lng1) * Math.PI / 180
	const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
		Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
		Math.sin(dLng / 2) * Math.sin(dLng / 2)
	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
	return R * c
}

/**
 * 格式化距离
 * @param {number} distance 距离（米）
 */
export function formatDistance(distance) {
	if (distance < 1000) {
		return Math.round(distance) + 'm'
	} else {
		return (distance / 1000).toFixed(1) + 'km'
	}
}

/**
 * 存储数据到本地
 * @param {string} key 键名
 * @param {any} data 数据
 */
export function setStorage(key, data) {
	try {
		uni.setStorageSync(key, data)
		return true
	} catch (error) {
		console.error('存储数据失败:', error)
		return false
	}
}

/**
 * 从本地获取数据
 * @param {string} key 键名
 * @param {any} defaultValue 默认值
 */
export function getStorage(key, defaultValue = null) {
	try {
		const data = uni.getStorageSync(key)
		return data !== '' ? data : defaultValue
	} catch (error) {
		console.error('获取数据失败:', error)
		return defaultValue
	}
}

/**
 * 删除本地数据
 * @param {string} key 键名
 */
export function removeStorage(key) {
	try {
		uni.removeStorageSync(key)
		return true
	} catch (error) {
		console.error('删除数据失败:', error)
		return false
	}
}

/**
 * 显示加载提示
 * @param {string} title 提示文字
 */
export function showLoading(title = '加载中...') {
	uni.showLoading({
		title: title,
		mask: true
	})
}

/**
 * 隐藏加载提示
 */
export function hideLoading() {
	uni.hideLoading()
}

/**
 * 显示成功提示
 * @param {string} title 提示文字
 */
export function showSuccess(title) {
	uni.showToast({
		title: title,
		icon: 'success',
		duration: 2000
	})
}

/**
 * 显示错误提示
 * @param {string} title 提示文字
 */
export function showError(title) {
	uni.showToast({
		title: title,
		icon: 'none',
		duration: 2000
	})
}

/**
 * 显示确认对话框
 * @param {string} content 内容
 * @param {string} title 标题
 */
export function showConfirm(content, title = '提示') {
	return new Promise((resolve) => {
		uni.showModal({
			title: title,
			content: content,
			success: (res) => {
				resolve(res.confirm)
			}
		})
	})
}
