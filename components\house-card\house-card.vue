<template>
	<view class="house-card" @click="goToDetail">
		<view class="card-image">
			<u-image 
				:src="houseInfo.images && houseInfo.images[0] || '/static/placeholder.jpg'" 
				mode="aspectFill"
				width="100%"
				height="300rpx"
				border-radius="20rpx 20rpx 0 0"
			></u-image>
			<view class="image-overlay">
				<view class="favorite-btn" @click.stop="toggleFavorite">
					<u-icon 
						:name="isFavorite ? 'heart-fill' : 'heart'" 
						:color="isFavorite ? '#ff6b35' : '#fff'"
						size="40"
					></u-icon>
				</view>
			</view>
		</view>
		
		<view class="card-content">
			<view class="house-title">
				<u-text 
					:text="houseInfo.title" 
					lines="2" 
					color="#333"
					size="30"
					bold
				></u-text>
			</view>
			
			<view class="house-desc">
				<u-text 
					:text="houseInfo.desc" 
					lines="1" 
					color="#666"
					size="26"
				></u-text>
			</view>
			
			<view class="house-tags" v-if="houseInfo.config && houseInfo.config.length">
				<u-tag 
					v-for="(tag, index) in houseInfo.config.slice(0, 3)" 
					:key="index"
					:text="tag"
					plain
					size="mini"
					type="primary"
					style="margin-right: 10rpx; margin-bottom: 10rpx;"
				></u-tag>
			</view>
			
			<view class="house-meta">
				<view class="price-section">
					<u-text 
						:text="`¥${houseInfo.price}`"
						color="#ff6b35"
						size="32"
						bold
					></u-text>
					<u-text 
						text="/月"
						color="#ff6b35"
						size="24"
						style="margin-left: 5rpx;"
					></u-text>
				</view>
				
				<view class="location-section">
					<u-icon name="map" color="#999" size="24"></u-icon>
					<u-text 
						:text="houseInfo.location?.address || '位置未知'"
						color="#999"
						size="24"
						style="margin-left: 5rpx;"
						lines="1"
					></u-text>
				</view>
			</view>
			
			<view class="house-footer">
				<view class="house-type">
					<u-tag 
						:text="houseInfo.type"
						type="info"
						size="mini"
						plain
					></u-tag>
				</view>
				
				<view class="house-time">
					<u-text 
						:text="formatTime(houseInfo.created_at)"
						color="#999"
						size="22"
					></u-text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { formatRelativeTime } from '@/utils/utils.js'

export default {
	name: 'HouseCard',
	props: {
		houseInfo: {
			type: Object,
			default: () => ({})
		},
		showFavorite: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			isFavorite: false
		}
	},
	mounted() {
		this.checkFavoriteStatus()
	},
	methods: {
		goToDetail() {
			if (this.houseInfo._id) {
				uni.navigateTo({
					url: `/pages/house-detail/house-detail?id=${this.houseInfo._id}`
				})
			}
		},
		
		async toggleFavorite() {
			if (!this.showFavorite) return
			
			try {
				// TODO: 调用API切换收藏状态
				this.isFavorite = !this.isFavorite
				
				uni.showToast({
					title: this.isFavorite ? '已收藏' : '已取消收藏',
					icon: 'none',
					duration: 1500
				})
				
				// 触发父组件事件
				this.$emit('favorite-change', {
					houseId: this.houseInfo._id,
					isFavorite: this.isFavorite
				})
			} catch (error) {
				console.error('切换收藏状态失败:', error)
			}
		},
		
		checkFavoriteStatus() {
			// TODO: 检查当前房源是否已收藏
			// 这里先模拟
			this.isFavorite = false
		},
		
		formatTime(time) {
			return formatRelativeTime(time)
		}
	}
}
</script>

<style lang="scss" scoped>
.house-card {
	background-color: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
	}
}

.card-image {
	position: relative;
	width: 100%;
	height: 300rpx;
}

.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 50%);
}

.favorite-btn {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	width: 60rpx;
	height: 60rpx;
	background-color: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
}

.card-content {
	padding: 30rpx;
}

.house-title {
	margin-bottom: 15rpx;
	min-height: 80rpx;
}

.house-desc {
	margin-bottom: 20rpx;
	min-height: 40rpx;
}

.house-tags {
	margin-bottom: 20rpx;
	min-height: 50rpx;
	display: flex;
	flex-wrap: wrap;
}

.house-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.price-section {
	display: flex;
	align-items: baseline;
}

.location-section {
	display: flex;
	align-items: center;
	flex: 1;
	margin-left: 20rpx;
	min-width: 0;
}

.house-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.house-type {
	flex-shrink: 0;
}

.house-time {
	flex-shrink: 0;
	margin-left: 20rpx;
}
</style>
